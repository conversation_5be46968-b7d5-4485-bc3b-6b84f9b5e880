import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { GridArtifact } from '@bika/domains/ai/client/chat/artifacts/grid-artifact';
import { FC } from 'react'
import { GridDataProps } from '@bika/domains/ai/client/chat/artifacts/grid-view-component';
import { NodeIcon } from '@bika/ui/node/icon';

// Mock data for GridDataProps
const mockGridData: GridDataProps = {
  isError: false,
  records: [
    {
      data: {
        name: '<PERSON>',
        age: 30,
        email: '<EMAIL>',
        status: 'active',
        isActive: true,
        salary: 75000,
      },
    },
    {
      data: {
        name: '<PERSON>',
        age: 25,
        email: '<EMAIL>',
        status: 'inactive',
        isActive: false,
        salary: 65000,
      },
    },
    {
      data: {
        name: '<PERSON>',
        age: 35,
        email: '<EMAIL>',
        status: 'pending',
        isActive: true,
        salary: 85000,
      },
    },
  ],
  fields: [
    { name: 'name', type: 'string' },
    { name: 'age', type: 'number' },
    { name: 'email', type: 'string' },
    { name: 'status', type: 'string' },
    { name: 'isActive', type: 'boolean' },
    { name: 'salary', type: 'number' },
  ],
};
const emptyGridData: GridDataProps = {
  records: [],
  isError: false,
  fields: [
    { name: 'name', type: 'string' },
    { name: 'age', type: 'number' },
    { name: 'email', type: 'string' },
    { name: 'status', type: 'string' },
    { name: 'isActive', type: 'boolean' },
    { name: 'salary', type: 'number' },
  ],
};

const singleRecordData: GridDataProps = {
  records: [mockGridData.records[0]],
  fields: mockGridData.fields,
};

const GridArtifactStory: FC<{data: GridDataProps, className?: string}> = ({data}) => {
  return (
    <>

    <div className="p-4 w-full h-[800px] w-[800px]">

      <NodeIcon value={{
         kind: 'icon', icon: 'components/service_outlined' 
      }}></NodeIcon>

      <GridArtifact data={data}></GridArtifact>
    </div>
    </>
  )
}
export default {
  title: '@bika/ai/GridArtifact',
  component: GridArtifactStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  args: {
    data: mockGridData,
  },
} satisfies Meta<typeof GridArtifact>;

type Story = StoryObj<typeof GridArtifact>;

export const Default: Story = {};

export const EmptyData: Story = {
  args: {
    data: emptyGridData,
  },
};

export const SingleRecord: Story = {
  args: {
    data: singleRecordData,
  },
};

export const LargeDataset: Story = {
  args: {
    data: {
      records: [
        ...mockGridData.records,
        ...Array.from({ length: 10 }, (_, index) => ({
          data: {
            name: `User ${index + 4}`,
            age: 20 + (index % 40),
            email: `user${index + 4}@example.com`,
            status: ['active', 'inactive', 'pending'][index % 3],
            isActive: index % 2 === 0,
            salary: 50000 + (index * 5000),
          },
        })),
      ],
      fields: mockGridData.fields,
    },
  },
};

export const ComplexFieldTypes: Story = {
  args: {
    data: {
      records: [
        {
          data: {
            description: 'This is a long text field with multiple lines of content that demonstrates how the grid artifact handles complex data types.',
            score: 95.5,
            completed: true,
            priority: 'high',
            rating: 4.8,
            isPublished: false,
          },
        },
        {
          data: {
            description: 'Another complex record with different values to show variety in the data display.',
            score: 87.2,
            completed: false,
            priority: 'medium',
            rating: 3.9,
            isPublished: true,
          },
        },
      ],
      fields: [
        { name: 'description', type: 'string' },
        { name: 'score', type: 'number' },
        { name: 'completed', type: 'boolean' },
        { name: 'priority', type: 'string' },
        { name: 'rating', type: 'number' },
        { name: 'isPublished', type: 'boolean' },
      ],
    },
  },
};
