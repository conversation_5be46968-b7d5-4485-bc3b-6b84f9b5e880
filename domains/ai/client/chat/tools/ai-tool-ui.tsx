'use client';

import React from 'react';
import { useApi<PERSON>aller } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceContextForce } from '@bika/types/space/context';
import { iStringParse } from '@bika/types/system';
import { Box } from '@bika/ui/layouts';
import { getDefaultToolUIContentProps, DefaultToolRenderer, ToolErrorRenderer } from './default-tool-renderer';
import type { ToolUIProps } from './type';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';
import type { ToolUIContentProps, ToolUIComponentProps } from '../../../../ai-skillset/types';
import { useBuildInstallGlobalStore } from '../../../../space/client/intercepter/use-build-install-global-store';

export function ToolUI(props: ToolUIProps) {
  const {
    part,
    hideFlow,
    skillsets,
    onClickTool,
    executeToolResult,
    addToolResult,
    sendMessage,
    sendUI,
    isHighlight,
    error,
  } = props;

  const localeContext = useLocale();
  const { setData: setBuildInstallData } = useBuildInstallGlobalStore();
  const spaceContext = useSpaceContextForce();

  const { useRootNode } = spaceContext || { useRootNode: () => ({ setRootNode: () => {} }) };
  const { setRootNode } = useRootNode();

  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(() => AISkillsetClientRegistry.getManySkillsetUI(skillsets), [skillsets]);
  const skillUICfg = React.useMemo(
    () => (skillsetUIMap ? skillsetUIMap[part.toolInvocation.toolName] : undefined),
    [skillsetUIMap, part.toolInvocation.toolName],
  );

  const handleClickTool = React.useMemo(
    () => () => {
      onClickTool(part.toolInvocation);
    },
    [onClickTool, part.toolInvocation],
  );

  const defaultToolContentProps: ToolUIContentProps = React.useMemo(
    () =>
      getDefaultToolUIContentProps(part.toolInvocation, {
        displayName: iStringParse(skillUICfg?.displayName, localeContext.lang),
        locale: localeContext,
      }),
    [part.toolInvocation, skillUICfg?.displayName, localeContext],
  );

  const apiCaller = useApiCaller();

  const doExecuteToolResult = React.useMemo(
    () => async (toolCallId: string) => {
      if (skillUICfg?.clientExecute) {
        // 如果有 clientExecute，说明是一个客户端的 Tool，执行后，可能会有结果更新
        return skillUICfg?.clientExecute?.(part.toolInvocation, {
          apiCaller,
          setData: setBuildInstallData,
          setRootNode,
        });
      }

      // Remote Server trpc Execute Tool
      return executeToolResult(toolCallId);
    },
    [skillUICfg, part.toolInvocation, apiCaller, setBuildInstallData, setRootNode, executeToolResult],
  );

  const renderToolComponentProps: ToolUIComponentProps = React.useMemo(
    () => ({
      executeToolResult: doExecuteToolResult,
      hideFlow,
      isHighlight,
      toolInvocation: part.toolInvocation,
      onClickTool: handleClickTool,
      addToolResult: (userResult) => {
        addToolResult({ toolCallId: part.toolInvocation.toolCallId, result: userResult });
      },
      sendMessage,
      sendUI,
      localeContext,
      skillsets,
    }),
    [
      doExecuteToolResult,
      hideFlow,
      isHighlight,
      part.toolInvocation,
      handleClickTool,
      addToolResult,
      sendMessage,
      sendUI,
      localeContext,
      skillsets,
    ],
  );

  const iconValue = undefined; //= useToolIcon({ skillsets: props.skillsets, toolInvocation: props.part.toolInvocation });

  // Memoized content props to prevent re-renders
  const memoizedContentProps = React.useMemo(
    () => ({
      ...defaultToolContentProps,
      icon: iconValue,
    }),
    [defaultToolContentProps, iconValue],
  );

  if (error && defaultToolContentProps) {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={skillsets}
          localeContext={localeContext}
          contentProps={memoizedContentProps}
          toolInvocation={part.toolInvocation}
          error={error}
        />
      </Box>
    );
  }

  if (
    part.toolInvocation.state === 'result' &&
    typeof part.toolInvocation.result === 'object' &&
    'error' in part.toolInvocation.result &&
    part.toolInvocation.result.error
  ) {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={skillsets}
          localeContext={localeContext}
          contentProps={memoizedContentProps}
          toolInvocation={part.toolInvocation}
          error={part.toolInvocation.result.error.message}
        />
      </Box>
    );
  }

  // 出默认，比如第三方 MCP，显示 tool-invocation 的内容
  if (skillUICfg?.component === undefined) {
    // const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part.toolInvocation} />;
    return (
      <Box mb={1}>
        <DefaultToolRenderer contentProps={memoizedContentProps} {...renderToolComponentProps} />
      </Box>
    );
  }

  // 定制整个空间，比如，做一个生图 UI Tool
  const componentResult = skillUICfg.component(renderToolComponentProps);
  if (typeof componentResult === 'object') {
    // const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part.toolInvocation} />;

    //  是否是配置
    const customContentProps = {
      icon: iconValue,
      ...componentResult,
    } as ToolUIContentProps;
    return (
      <Box mb={1}>
        <DefaultToolRenderer
          contentProps={{
            ...defaultToolContentProps,
            ...customContentProps,
          }}
          {...renderToolComponentProps}
        />
      </Box>
    );
  }
  return (
    <>
      <Box mb={1}>{componentResult}</Box>
    </>
  );
}
